# 🚀 Universal AI Assistants - Cloud Optimized Dockerfile
# تطبيق محسن للسحابة مع تحميل النماذج عند الطلب

FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV GOOGLE_CLOUD_PROJECT=universal-ai-assistants-2025
ENV MODELS_BUCKET=universal-ai-models-2025-storage

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# تثبيت Google Cloud SDK
RUN curl https://sdk.cloud.google.com | bash
ENV PATH="/root/google-cloud-sdk/bin:${PATH}"

# تثبيت Ollama
RUN curl -fsSL https://ollama.ai/install.sh | sh

# إعداد مجلد العمل
WORKDIR /app

# نسخ متطلبات Python أولاً (للاستفادة من Docker cache)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ مدير النماذج السحابية
COPY cloud_models_manager.py .
COPY upload_models_to_gcloud.py .

# نسخ باقي ملفات التطبيق
COPY . .

# إنشاء مجلدات مطلوبة
RUN mkdir -p /tmp/ollama_models_cache
RUN mkdir -p /root/.ollama/models

# إنشاء سكريبت بدء التشغيل المحسن
RUN cat > /app/start_cloud_optimized.sh << 'EOF'
#!/bin/bash

echo "🚀 بدء Universal AI Assistants مع النماذج السحابية..."

# بدء خدمة Ollama في الخلفية
echo "⚡ بدء خدمة Ollama..."
ollama serve &
OLLAMA_PID=$!

# انتظار بدء Ollama
echo "⏳ انتظار بدء Ollama..."
sleep 15

# التحقق من حالة Ollama
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ فشل في بدء Ollama"
    exit 1
fi

echo "✅ Ollama جاهز"

# تحميل النماذج من Cloud Storage (في الخلفية)
echo "📥 بدء تحميل النماذج من Cloud Storage..."
python cloud_models_manager.py setup &
MODELS_PID=$!

# بدء التطبيق الرئيسي
echo "🌐 بدء التطبيق الرئيسي..."
python main.py &
APP_PID=$!

# انتظار اكتمال تحميل النماذج
echo "⏳ انتظار اكتمال تحميل النماذج..."
wait $MODELS_PID

echo "✅ النماذج جاهزة"
echo "🎉 Universal AI Assistants جاهز للاستخدام!"

# انتظار التطبيق الرئيسي
wait $APP_PID
EOF

# جعل السكريبت قابل للتنفيذ
RUN chmod +x /app/start_cloud_optimized.sh

# إنشاء سكريبت فحص الصحة
RUN cat > /app/health_check.sh << 'EOF'
#!/bin/bash

# فحص حالة التطبيق الرئيسي
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ التطبيق الرئيسي غير متاح"
    exit 1
fi

# فحص حالة Ollama
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "❌ Ollama غير متاح"
    exit 1
fi

echo "✅ جميع الخدمات تعمل بنجاح"
exit 0
EOF

RUN chmod +x /app/health_check.sh

# تعريف المنفذ
EXPOSE 8080

# إعداد فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD /app/health_check.sh

# تشغيل التطبيق
CMD ["/app/start_cloud_optimized.sh"]
