# 🚀 Universal AI Assistants - Simple Cloud Dockerfile
# نسخة مبسطة للاختبار السريع مع إمكانية تحميل النماذج

FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV GOOGLE_CLOUD_PROJECT=universal-ai-assistants-2025
ENV MODELS_BUCKET=universal-ai-models-2025-storage

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# إعداد مجلد العمل
WORKDIR /app

# نسخ متطلبات Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY . .

# تعريف المنفذ
EXPOSE 8080

# تشغيل التطبيق
CMD ["python", "main.py"]
