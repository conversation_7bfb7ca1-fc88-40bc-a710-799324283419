#!/usr/bin/env python3
"""
☁️ Universal AI Assistants - Cloud Models Manager
إدارة النماذج في Google Cloud واستخدامها عند الطلب
"""

import os
import subprocess
import json
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Optional

class CloudModelsManager:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.bucket_name = "universal-ai-models-2025-storage"
        self.local_cache = Path("/tmp/ollama_models_cache")
        self.ollama_models_path = Path.home() / ".ollama" / "models"
        
    def check_gcloud_auth(self):
        """التحقق من تسجيل الدخول في Google Cloud"""
        try:
            result = subprocess.run("gcloud auth list --filter=status:ACTIVE --format='value(account)'", 
                                  shell=True, capture_output=True, text=True)
            if result.stdout.strip():
                print(f"✅ مسجل الدخول كـ: {result.stdout.strip()}")
                return True
            else:
                print("❌ غير مسجل الدخول في Google Cloud")
                return False
        except Exception as e:
            print(f"❌ خطأ في التحقق من المصادقة: {e}")
            return False
    
    def list_cloud_models(self) -> List[Dict]:
        """عرض النماذج المتاحة في Cloud Storage"""
        print("📋 جمع قائمة النماذج من Google Cloud Storage...")
        
        try:
            # تحميل metadata
            cmd = f"gsutil cp gs://{self.bucket_name}/metadata/models_metadata.json /tmp/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                with open("/tmp/models_metadata.json", "r") as f:
                    metadata = json.load(f)
                    return metadata.get("models", [])
            else:
                print(f"❌ خطأ في تحميل metadata: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return []
    
    def download_model_on_demand(self, model_name: str) -> bool:
        """تحميل نموذج محدد عند الطلب"""
        print(f"📥 تحميل نموذج {model_name} عند الطلب...")
        
        try:
            # إنشاء مجلد cache إذا لم يكن موجوداً
            self.local_cache.mkdir(parents=True, exist_ok=True)
            
            # تحميل النموذج المحدد
            model_path = f"gs://{self.bucket_name}/ollama/blobs/*"
            cmd = f"gsutil -m cp -r {model_path} {self.local_cache}/"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تحميل {model_name} بنجاح")
                return True
            else:
                print(f"❌ خطأ في تحميل {model_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False
    
    def setup_ollama_with_cloud_models(self):
        """إعداد Ollama مع النماذج من Cloud Storage"""
        print("🔧 إعداد Ollama مع النماذج من Cloud Storage...")
        
        try:
            # إنشاء مجلد النماذج
            self.ollama_models_path.mkdir(parents=True, exist_ok=True)
            
            # تحميل جميع النماذج
            cmd = f"gsutil -m cp -r gs://{self.bucket_name}/ollama/* {self.ollama_models_path}/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم إعداد Ollama مع النماذج من Cloud")
                
                # إعادة تشغيل Ollama
                subprocess.run("pkill ollama", shell=True, capture_output=True)
                subprocess.run("ollama serve &", shell=True, capture_output=True)
                
                return True
            else:
                print(f"❌ خطأ في إعداد النماذج: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False
    
    def create_cloud_run_dockerfile(self):
        """إنشاء Dockerfile محسن لـ Cloud Run مع النماذج"""
        dockerfile_content = """
# Dockerfile محسن لـ Universal AI Assistants مع النماذج
FROM python:3.11-slim

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \\
    curl \\
    wget \\
    git \\
    && rm -rf /var/lib/apt/lists/*

# تثبيت Google Cloud SDK
RUN curl https://sdk.cloud.google.com | bash
ENV PATH="/root/google-cloud-sdk/bin:${PATH}"

# تثبيت Ollama
RUN curl -fsSL https://ollama.ai/install.sh | sh

# إعداد مجلد العمل
WORKDIR /app

# نسخ متطلبات Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# إنشاء سكريبت بدء التشغيل
RUN echo '#!/bin/bash\\n\\
# بدء خدمة Ollama في الخلفية\\n\\
ollama serve &\\n\\
\\n\\
# انتظار بدء Ollama\\n\\
sleep 10\\n\\
\\n\\
# تحميل النماذج من Cloud Storage عند الطلب\\n\\
python cloud_models_manager.py setup\\n\\
\\n\\
# بدء التطبيق\\n\\
python main.py' > /app/start.sh

RUN chmod +x /app/start.sh

# تعريف المنفذ
EXPOSE 8080

# تشغيل التطبيق
CMD ["/app/start.sh"]
"""
        
        with open("Dockerfile.cloud-models", "w") as f:
            f.write(dockerfile_content)
        
        print("✅ تم إنشاء Dockerfile محسن للنماذج السحابية")
    
    def create_models_api(self):
        """إنشاء API لإدارة النماذج"""
        api_content = '''
from flask import Flask, jsonify, request
import subprocess
import json

app = Flask(__name__)
models_manager = CloudModelsManager()

@app.route('/models/list')
def list_models():
    """عرض النماذج المتاحة"""
    models = models_manager.list_cloud_models()
    return jsonify({"models": models, "status": "success"})

@app.route('/models/download/<model_name>')
def download_model(model_name):
    """تحميل نموذج محدد"""
    success = models_manager.download_model_on_demand(model_name)
    return jsonify({"model": model_name, "downloaded": success})

@app.route('/models/generate', methods=['POST'])
def generate_with_model():
    """توليد نص باستخدام نموذج محدد"""
    data = request.json
    model = data.get('model', 'phi3:mini')
    prompt = data.get('prompt', '')
    
    try:
        cmd = f'ollama run {model} "{prompt}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        return jsonify({
            "model": model,
            "prompt": prompt,
            "response": result.stdout,
            "status": "success"
        })
    except Exception as e:
        return jsonify({"error": str(e), "status": "error"})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
'''
        
        with open("models_api.py", "w") as f:
            f.write(api_content)
        
        print("✅ تم إنشاء API لإدارة النماذج")
    
    def run_command(self, command: str):
        """تنفيذ أوامر إدارة النماذج"""
        if command == "upload":
            # رفع النماذج إلى Cloud Storage
            uploader = ModelsUploader()
            uploader.run_upload()
            
        elif command == "setup":
            # إعداد النماذج من Cloud Storage
            if self.check_gcloud_auth():
                self.setup_ollama_with_cloud_models()
            else:
                print("❌ يرجى تسجيل الدخول أولاً: gcloud auth login")
                
        elif command == "list":
            # عرض النماذج المتاحة
            models = self.list_cloud_models()
            print("\n📋 النماذج المتاحة في Cloud Storage:")
            for model in models:
                print(f"  • {model['name']} ({model['size']})")
                
        elif command == "create-dockerfile":
            # إنشاء Dockerfile محسن
            self.create_cloud_run_dockerfile()
            
        elif command == "create-api":
            # إنشاء API للنماذج
            self.create_models_api()
            
        else:
            print("❌ أمر غير معروف")
            print("الأوامر المتاحة: upload, setup, list, create-dockerfile, create-api")

def main():
    import sys
    
    manager = CloudModelsManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        manager.run_command(command)
    else:
        print("🎯 Universal AI Assistants - Cloud Models Manager")
        print("الاستخدام: python cloud_models_manager.py [command]")
        print("\nالأوامر المتاحة:")
        print("  upload          - رفع النماذج إلى Cloud Storage")
        print("  setup           - إعداد النماذج من Cloud Storage")
        print("  list            - عرض النماذج المتاحة")
        print("  create-dockerfile - إنشاء Dockerfile محسن")
        print("  create-api      - إنشاء API لإدارة النماذج")

if __name__ == "__main__":
    main()
